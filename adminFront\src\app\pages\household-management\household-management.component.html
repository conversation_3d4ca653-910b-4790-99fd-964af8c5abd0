<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label col-3">建案</label>
          <nb-select placeholder="建案" [(ngModel)]="searchQuery.CBuildCaseSelected" class="col-9"
            (selectedChange)="onSelectionChangeBuildCase()">
            <nb-option *ngFor="let case of userBuildCaseOptions" [value]="case">
              {{ case.CBuildCaseName }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cHouseType" class="label col-3">類型</label>
          <nb-select [(ngModel)]="searchQuery.CHouseTypeSelected" class="col-9">
            <nb-option *ngFor="let case of houseTypeOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center">
          <label for="cFloorFrom" class="label col-3">樓
          </label>
          <nb-form-field class="ml-3">
            <input type="text" id="CFrom" nbInput class="w-full col-4" [(ngModel)]="searchQuery.CFrom">
          </nb-form-field>
          <label for="cFloorTo" class="label col-1">~
          </label>
          <nb-form-field class="mr-3">
            <input type="text" id="CTo" nbInput class="w-full" [(ngModel)]="searchQuery.CTo">
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <!-- <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label col-3">
            棟別
          </label>
          <nb-select [(ngModel)]="searchQuery.CBuildingNameSelected" class="col-9">
            <nb-option *ngFor="let case of buildingSelectedOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div> -->
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cHousehold" class="label col-3">
            戶型
          </label>
          <nb-select placeholder="戶型" [(ngModel)]="searchQuery.CHouseHoldSelected" class="col-9">
            <nb-option *ngFor="let case of houseHoldOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cPayStatus" class="label col-3">
            繳款狀態
          </label>
          <nb-select placeholder="繳款狀態" [(ngModel)]="searchQuery.CPayStatusSelected" class="col-9">
            <nb-option *ngFor="let case of payStatusOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cProgress" class="label col-3">
            進度
          </label>
          <nb-select placeholder="進度" [(ngModel)]="searchQuery.CProgressSelected" class="col-9">
            <nb-option *ngFor="let case of progressOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cStatus" class="label col-3">
            狀態
          </label>
          <nb-select placeholder="狀態" [(ngModel)]="searchQuery.CIsEnableSeleted" class="col-9">
            <nb-option *ngFor="let case of cIsEnableOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cSignStatus" class="label col-3">
            簽回狀態
          </label>
          <nb-select placeholder="簽回狀態" [(ngModel)]="searchQuery.CSignStatusSelected" class="col-9">
            <nb-option *ngFor="let case of signStatusOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cQuotationStatus" class="label col-3">
            報價單狀態
          </label>
          <nb-select placeholder="報價單狀態" [(ngModel)]="searchQuery.CQuotationStatusSelected" class="col-9">
            <nb-option *ngFor="let case of quotationStatusOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->
      </div>

      <!-- 查詢按鈕移到這裡，放在搜尋條件的右下角 -->
      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-2 mb-3">
          <button class="btn btn-secondary btn-sm" (click)="onSearch()">
            查詢 <i class="fas fa-search"></i>
          </button>
        </div>
      </div>

      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-3">
          <button class="btn btn-info mx-1 btn-sm" *ngIf="isCreate" (click)="openModel(dialogHouseholdMain)">
            批次新增戶別資料
          </button>
          <button class="btn btn-info mx-1 btn-sm" (click)="onNavidateId('modify-floor-plan')">
            修改樓層戶型
          </button>
          <!-- <button class="btn btn-info mx-1 btn-sm" (click)="onNavidateId('standard-house-plan')">
            3.設定戶型標準圖
          </button> -->
          <button class="btn btn-info mx-1 btn-sm" (click)="exportHouse()">
            匯出戶別明細檔
          </button>
          <input type="file" #fileInput style="display:none" (change)="onFileSelected($event)" accept=".xlsx, .xls" />
          <button class="btn btn-info btn-sm" (click)="triggerFileInput()">
            匯入更新戶別明細檔
          </button>
        </div>
      </div>
    </div>

    <div class="table-responsive mt-4">
      <table class="table table-striped border " style="min-width: 1000px; background-color:#f3f3f3;">
        <thead>
          <tr style="background-color: #27ae60; color: white;">
            <!-- <th scope="col" class="col-1">棟別</th> -->
            <th scope="col" class="col-1">戶型</th>
            <th scope="col" class="col-1">樓層</th>
            <th scope="col" class="col-1">戶別</th>
            <th scope="col" class="col-1">類型</th>
            <th scope="col" class="col-1">進度</th>
            <th scope="col" class="col-1">繳款狀態</th>
            <th scope="col" class="col-1">簽回狀態</th>
            <th scope="col" class="col-1">報價單狀態</th>
            <th scope="col" class="col-1">狀態</th>
            <th scope="col" class="col-4">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of houseList ; let i = index">
            <!-- <td>{{ item.CBuildingName}}</td> -->
            <td>{{ item.CHouseHold}}</td>
            <td>{{ item.CFloor}}</td>
            <td>
              {{ item.CHouseType === 2 ? '銷售戶' : ''}}
              {{item.CHouseType === 1 ? '地主戶' : ''}}
            </td>
            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>
            <td>{{ item.CProgressName}}</td>
            <td>
              {{item.CPayStatus === 0 ? '未付款': ''}}
              {{item.CPayStatus === 1 ? '已付款': ''}}
              {{item.CPayStatus === 2 ? '無須付款': ''}}
            </td>
            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>
            <td>{{ getQuotationStatusText(item.CQuotationStatus) }}</td>
            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>
            <td class="text-center w-32 px-0">
              <button *ngIf="isUpdate" class="btn btn-outline-success btn-sm text-left m-[2px]"
                (click)="openModelDetail(dialogUpdateHousehold, item)">
                編輯
              </button>
              <button class="btn btn-outline-success btn-sm m-[2px]"
                (click)="onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )">
                洽談紀錄
              </button>
              <button class="btn btn-outline-success btn-sm m-[2px]"
                (click)="onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )">
                客變確認圖說
              </button>
              <button class="btn btn-outline-success btn-sm m-[2px]"
                (click)="onNavidateBuildCaseIdHouseId('finaldochouse_management', searchQuery.CBuildCaseSelected.cID, item.CID )">
                簽署文件歷程
              </button>
              <button class="btn btn-outline-success btn-sm m-[2px]" (click)="resetSecureKey(item)">
                重置密碼
              </button>
              <button class="btn btn-outline-success btn-sm m-[2px]" (click)="openQuotation(dialogQuotation, item)">
                報價單
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngb-pagination [(page)]="pageIndex" [pageSize]="pageSize" [collectionSize]="totalRecords"
      (pageChange)="pageChanged($event)" aria-label="Pagination">
    </ngb-pagination>
  </nb-card-footer>
</nb-card>

<ng-template #dialogUpdateHousehold let-dialog let-ref="dialogRef">
  <nb-card style="width:500px; max-height: 95vh">
    <!-- <nb-card-header>
    </nb-card-header> -->
    <nb-card-body class="px-4" *ngIf="houseDetail">
      <div class="form-group">
        <label for="cBuildCaseId" class="required-field mr-4" style="min-width:75px" baseLabel>
          建案名稱
        </label>
        <nb-select placeholder="建案名稱" [(ngModel)]="detailSelected.CBuildCaseSelected" class="w-full" disabled="true">
          <nb-option *ngFor="let status of userBuildCaseOptions" [value]="status">
            {{ status.CBuildCaseName }}
          </nb-option>
        </nb-select>
      </div>

      <div class="form-group">
        <label for="cHousehold" class="required-field mr-4" style="min-width:75px" baseLabel>
          戶型名稱
        </label>
        <input type="text" class="w-full" nbInput placeholder="戶型名稱" [(ngModel)]="houseDetail.CHousehold" />
      </div>

      <div class="form-group">
        <label for="cFloor" class="required-field mr-4" style="min-width:75px" baseLabel>
          樓層
        </label>
        <input type="number" class="w-full" nbInput placeholder="樓層" [(ngModel)]="houseDetail.CFloor" min="1" max="100"
          disabled="true" />
      </div>

      <div class="form-group">
        <label for="cCustomerName" class="mr-4" style="min-width:75px" baseLabel>
          客戶姓名
        </label>
        <input type="text" class="w-full" nbInput placeholder="客戶姓名" [(ngModel)]="houseDetail.CCustomerName" />
      </div>

      <div class="form-group">
        <label for="cNationalId" class="mr-4" style="min-width:75px" baseLabel>
          身分證字號
        </label>
        <input type="text" class="w-full" nbInput placeholder="身分證字號" [(ngModel)]="houseDetail.CNationalId"
          maxlength="20" />
      </div>

      <div class="form-group">
        <label for="cMail" class="mr-4" style="min-width:75px" baseLabel>
          電子郵件
        </label>
        <input type="text" class="w-full" nbInput placeholder="電子郵件" [(ngModel)]="houseDetail.CMail" />
      </div>
      <div class="form-group">
        <label for="cPhone" class="mr-4" style="min-width:75px" baseLabel>
          聯絡電話
        </label>
        <input type="text" class="w-full" nbInput placeholder="聯絡電話" [(ngModel)]="houseDetail.CPhone" />
      </div>

      <div class="form-group">
        <label for="cHouseType" class="required-field mr-4" style="min-width:75px" baseLabel>
          戶別類型
        </label>
        <nb-select placeholder="戶別類型" [(ngModel)]="detailSelected.CHouseTypeSelected" class="w-full">
          <nb-option *ngFor="let status of options.houseTypeOptions" [value]="status">
            {{ status.label }}
          </nb-option>
        </nb-select>
      </div>

      <div class="form-group" *ngIf="isChangePayStatus">
        <label for="cPayStatus" class="required-field mr-4" style="min-width:75px" baseLabel>
          付款狀態
        </label>
        <nb-select placeholder="付款狀態" [(ngModel)]="detailSelected.CPayStatusSelected" class="w-full">
          <nb-option *ngFor="let status of options.payStatusOptions" [value]="status">
            {{ status.label }}
          </nb-option>
        </nb-select>
      </div>
      <div class="form-group" *ngIf="isChangeProgress">
        <label for="cProgress" class="required-field mr-4" style="min-width:75px" baseLabel>
          進度
        </label>
        <nb-select placeholder="進度" [(ngModel)]="detailSelected.CProgressSelected" class="w-full">
          <nb-option *ngFor="let status of options.progressOptions" [value]="status">
            {{ status.label }}
          </nb-option>
        </nb-select>
      </div>

      <div class="form-group">
        <label for="cIsChange" class="mr-4" style="min-width:75px" baseLabel>
          是否客變
        </label>
        <nb-checkbox status="basic" [(checked)]="houseDetail.CIsChange">是
        </nb-checkbox>
      </div>

      <div class="form-group">
        <label for="cIsEnable" class="mr-4" style="min-width:75px" baseLabel>
          是否啟用
        </label>
        <nb-checkbox status="basic" [(checked)]="houseDetail.CIsEnable">是
        </nb-checkbox>
      </div>

      <div class="form-group flex flex-row">
        <label for="cIsEnable" class="mr-4 content-center" style="min-width:75px" baseLabel>
          客變時段
        </label>
        <div class="max-w-xs flex flex-row">
          <nb-form-field class="w-1/2">
            <nb-icon nbPrefix icon="calendar-outline"></nb-icon>
            <input nbInput type="text" id="StartDate" placeholder="yyyy-mm-dd" [nbDatepicker]="StartDate"
              class="w-[42%] mr-2" [(ngModel)]="houseDetail.changeStartDate">
            <nb-datepicker #StartDate format="yyyy-MM-dd"></nb-datepicker>
          </nb-form-field>
          <nb-form-field class="w-1/2">
            <nb-icon nbPrefix icon="calendar-outline"></nb-icon>
            <input nbInput type="text" id="EndDate" placeholder="yyyy-mm-dd" [nbDatepicker]="EndDate"
              class="w-[42%] ml-2" [(ngModel)]="houseDetail.changeEndDate">
            <nb-datepicker #EndDate format="yyyy-MM-dd"></nb-datepicker>
          </nb-form-field>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-center">
      <button class="btn btn-outline-secondary m-2 px-8" (click)="onClose(ref)">取消</button>
      <button class="btn btn-primary m-2 bg-[#169BD5] px-8" *ngIf="isCreate" (click)="onSubmitDetail(ref)">送出</button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #dialogHouseholdMain let-dialog let-ref="dialogRef">
  <nb-card style="width:500px; max-height: 95vh">
    <nb-card-header>
      戶別管理 》批次新增戶別資料
    </nb-card-header>
    <nb-card-body class="px-4">
      <div class="form-group">
        <label for="cBuildingName" class="required-field mr-4" style="min-width:75px" baseLabel>
          棟別
        </label>
        <input type="text" class="w-full" nbInput placeholder="棟別" [(ngModel)]="houseHoldMain.CBuildingName" />
      </div>
      <div class="form-group">
        <label for="CHouseHoldCount" class="required-field mr-4" style="min-width:75px" baseLabel>當層最多戶數
        </label>
        <input type="number" class="w-full" nbInput placeholder="當層最多戶數" [(ngModel)]="houseHoldMain.CHouseHoldCount" />
      </div>
      <div class="form-group">
        <label for="CFloor" class="required-field mr-4" style="min-width:75px" baseLabel>本棠總樓層
        </label>
        <input type="number" class="w-full" nbInput placeholder="本棠總樓層" [(ngModel)]="houseHoldMain.CFloor" />
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-center">
      <button class="btn btn-primary mr-4" (click)="onClose(ref)">{{ '關閉'}}</button>
      <button class="btn btn-primary" *ngIf="isCreate" (click)="addHouseHoldMain(ref)">儲存</button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 報價單對話框 -->
<ng-template #dialogQuotation let-dialog let-ref="dialogRef">
  <nb-card style="width:1200px; max-height: 95vh">
    <nb-card-header>
      報價單 - {{ currentHouse?.CHouseHold }} ({{ currentHouse?.CFloor }}樓)
    </nb-card-header>
    <nb-card-body>
      <!-- 只有報價單可編輯時才顯示操作按鈕 -->
      <div *ngIf="isQuotationEditable" class="mb-4 d-flex justify-content-between">
        <button class="btn btn-info btn-sm" (click)="addQuotationItem()">
          + 新增自定義項目
        </button>
        <div>
          <button class="btn btn-secondary btn-sm me-2" (click)="loadDefaultItems()">
            載入客變需求
          </button>
          <button class="btn btn-secondary btn-sm" (click)="loadRegularItems()">
            載入選樣資料
          </button>
        </div>
      </div>

      <!-- 報價單已鎖定時的提示 -->
      <div *ngIf="!isQuotationEditable" class="mb-4 alert alert-warning">
        <i class="fas fa-lock me-2"></i>
        <strong>報價單已鎖定</strong> - 此報價單已鎖定，無法進行修改。您可以列印此報價單或產生新的報價單。
      </div>

      <div class="table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th width="25%">項目名稱</th>
              <th width="15%">單價 (元)</th>
              <th width="8%">單位</th>
              <th width="8%">數量</th>
              <th width="18%">小計 (元)</th>
              <th width="10%">類型</th>
              <th width="10%">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of quotationItems; let i = index">
              <td>
                <input type="text" nbInput [(ngModel)]="item.cItemName"
                  [disabled]="!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3"
                  class="w-full"
                  [class.bg-light]="!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3">
              </td>
              <td>
                <input type="number" nbInput [(ngModel)]="item.cUnitPrice" (ngModelChange)="calculateTotal()"
                  class="w-full" min="0" step="0.01" [disabled]="!isQuotationEditable || item.CQuotationItemType === 3">
              </td>
              <td>
                <input type="text" nbInput [(ngModel)]="item.cUnit"
                  [disabled]="!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3"
                  class="w-full" placeholder="單位"
                  [class.bg-light]="!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3">
              </td>
              <td>
                <input type="number" nbInput [(ngModel)]="item.cCount" (ngModelChange)="calculateTotal()" class="w-full"
                  step="0.01" [disabled]="!isQuotationEditable || item.CQuotationItemType === 3">
              </td>
              <td class="text-right">
                {{ formatCurrency(item.cUnitPrice * item.cCount) }}
              </td>
              <td>
                <span class="badge" [class.badge-primary]="item.CQuotationItemType === 1"
                  [class.badge-info]="item.CQuotationItemType === 3"
                  [class.badge-secondary]="item.CQuotationItemType !== 1 && item.CQuotationItemType !== 3">
                  {{ getQuotationTypeText(item.CQuotationItemType) }}
                </span>
              </td>
              <td>
                <button *ngIf="isQuotationEditable" class="btn btn-danger btn-sm" (click)="removeQuotationItem(i)">
                  刪除
                </button>
                <span *ngIf="!isQuotationEditable" class="text-muted">
                  <i class="fas fa-lock"></i>
                </span>
              </td>
            </tr>
            <tr *ngIf="quotationItems.length === 0">
              <td colspan="7" class="text-center text-muted py-4">
                請點擊「新增自定義項目」或「載入客變需求」開始建立報價單
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="text-right mt-4">
        <div class="mb-3">
          <h5 class="text-secondary">小計: {{ formatCurrency(totalAmount) }}</h5>
        </div>

        <!-- 營業稅資訊顯示 (固定5%) -->
        <div class="mb-3 p-3 border rounded bg-light">
          <div class="row align-items-center">
            <div class="col-md-6">
              <label class="form-label mb-1">營業稅 (5%)：</label>
              <div class="text-info fw-bold">{{ formatCurrency(additionalFeeAmount) }}</div>
            </div>
            <div class="col-md-6">
              <small class="text-muted">* 營業稅固定為小計金額的5%</small>
            </div>
          </div>
        </div>

        <div class="border-top pt-2">
          <h4 class="text-primary fw-bold">總金額: {{ formatCurrency(finalTotalAmount) }}</h4>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-between">
      <div>
        <button class="btn btn-outline-info btn-sm me-2" (click)="printQuotation()"
          [disabled]="quotationItems.length === 0" title="列印報價單">
          <i class="fas fa-print me-1"></i> 列印報價單
        </button>
        <!-- 報價單已鎖定時才顯示 -->
        <button *ngIf="!isQuotationEditable" class="btn btn-outline-success btn-sm me-2" (click)="createNewQuotation()"
          title="產生新報價單">
          <i class="fas fa-plus me-1"></i> 產生新報價單
        </button>
        <!-- <button class="btn btn-outline-info btn-sm" (click)="exportQuotation()"
          [disabled]="quotationItems.length === 0">
          匯出報價單
        </button> -->
      </div>
      <div>
        <button class="btn btn-outline-secondary m-2" (click)="onClose(ref)">取消</button>
        <!-- 只有在報價單可編輯時才顯示鎖定和儲存按鈕 -->
        <button *ngIf="isQuotationEditable" class="btn btn-warning m-2" (click)="lockQuotation(ref)"
          [disabled]="quotationItems.length === 0">
          鎖定報價單
        </button>
        <button *ngIf="isQuotationEditable" class="btn btn-primary m-2" (click)="saveQuotation(ref)"
          [disabled]="quotationItems.length === 0">
          儲存報價單
        </button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>
